{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0885c2c8-ccd9-4809-9606-215ff312d61d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Failed to read as a list. Trying line-delimited JSON...\n", "CSV file saved as News_Category_Dataset_v3.csv\n"]}], "source": ["import json\n", "import pandas as pd\n", "\n", "input_path = \"News_Category_Dataset_v3.json\"\n", "output_path = \"News_Category_Dataset_v3.csv\"\n", "\n", "# Try to read as a list of JSON objects\n", "try:\n", "    with open(input_path, \"r\", encoding=\"utf-8\") as f:\n", "        data = json.load(f)\n", "    df = pd.DataFrame(data)\n", "    df.to_csv(output_path, index=False)\n", "    print(f\"CSV file saved as {output_path}\")\n", "except Exception as e:\n", "    print(\"Failed to read as a list. Trying line-delimited JSON...\")\n", "    # Try to read as line-delimited JSON\n", "    try:\n", "        df = pd.read_json(input_path, lines=True)\n", "        df.to_csv(output_path, index=False)\n", "        print(f\"CSV file saved as {output_path}\")\n", "    except Exception as e2:\n", "        print(\"Failed to convert JSON to CSV.\")\n", "        print(\"Error 1:\", e)\n", "        print(\"Error 2:\", e2)\n", " \n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "0ab36133-c0e9-410b-bb09-35ee9155c33c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>link</th>\n", "      <th>headline</th>\n", "      <th>category</th>\n", "      <th>short_description</th>\n", "      <th>authors</th>\n", "      <th>date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>https://www.huffpost.com/entry/covid-boosters-...</td>\n", "      <td>Over 4 Million Americans Roll Up Sleeves For O...</td>\n", "      <td>U.S. NEWS</td>\n", "      <td>Health experts said it is too early to predict...</td>\n", "      <td><PERSON>, AP</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>https://www.huffpost.com/entry/american-airlin...</td>\n", "      <td>American Airlines Flyer Charged, Banned For Li...</td>\n", "      <td>U.S. NEWS</td>\n", "      <td>He was subdued by passengers and crew when he ...</td>\n", "      <td><PERSON></td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>https://www.huffpost.com/entry/funniest-tweets...</td>\n", "      <td>23 Of The Funniest Tweets About Cats And Dogs ...</td>\n", "      <td>COMEDY</td>\n", "      <td>\"Until you have a dog you don't understand wha...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>https://www.huffpost.com/entry/funniest-parent...</td>\n", "      <td>The Funniest Tweets From Parents This Week (Se...</td>\n", "      <td>PARENTING</td>\n", "      <td>\"Accidentally put grown-up toothpaste on my to...</td>\n", "      <td><PERSON></td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>https://www.huffpost.com/entry/amy-cooper-lose...</td>\n", "      <td>Woman Who Called Cops On Black Bird-Watcher Lo...</td>\n", "      <td>U.S. NEWS</td>\n", "      <td><PERSON> accused investment firm Franklin Te...</td>\n", "      <td><PERSON></td>\n", "      <td>2022-09-22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>https://www.huffpost.com/entry/belk-worker-fou...</td>\n", "      <td><PERSON><PERSON> Was Dead In Belk Bathroom For 4 Days B...</td>\n", "      <td>U.S. NEWS</td>\n", "      <td>The 63-year-old woman was seen working at the ...</td>\n", "      <td>NaN</td>\n", "      <td>2022-09-22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>https://www.huffpost.com/entry/reporter-gets-a...</td>\n", "      <td>Reporter Gets Adorable Surprise From Her Boyfr...</td>\n", "      <td>U.S. NEWS</td>\n", "      <td>\"Who's that behind you?\" an anchor for New Yor...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>2022-09-22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>https://www.huffpost.com/entry/puerto-rico-wat...</td>\n", "      <td>Puerto Ricans Desperate For Water After Hurric...</td>\n", "      <td>WORLD NEWS</td>\n", "      <td>More than half a million people remained witho...</td>\n", "      <td>DÁNICA COTO, AP</td>\n", "      <td>2022-09-22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>https://www.huffpost.com/entry/mija-documentar...</td>\n", "      <td>How A New Documentary Captures The Complexity ...</td>\n", "      <td>CULTURE &amp; ARTS</td>\n", "      <td>In \"Mija,\" director <PERSON> combined mus...</td>\n", "      <td><PERSON></td>\n", "      <td>2022-09-22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>https://www.huffpost.com/entry/biden-un-russia...</td>\n", "      <td><PERSON><PERSON> At UN To Call Russian War An Affront To ...</td>\n", "      <td>WORLD NEWS</td>\n", "      <td>White House officials say the crux of the pres...</td>\n", "      <td><PERSON><PERSON><PERSON>, AP</td>\n", "      <td>2022-09-21</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                link  \\\n", "0  https://www.huffpost.com/entry/covid-boosters-...   \n", "1  https://www.huffpost.com/entry/american-airlin...   \n", "2  https://www.huffpost.com/entry/funniest-tweets...   \n", "3  https://www.huffpost.com/entry/funniest-parent...   \n", "4  https://www.huffpost.com/entry/amy-cooper-lose...   \n", "5  https://www.huffpost.com/entry/belk-worker-fou...   \n", "6  https://www.huffpost.com/entry/reporter-gets-a...   \n", "7  https://www.huffpost.com/entry/puerto-rico-wat...   \n", "8  https://www.huffpost.com/entry/mija-documentar...   \n", "9  https://www.huffpost.com/entry/biden-un-russia...   \n", "\n", "                                            headline        category  \\\n", "0  Over 4 Million Americans Roll Up Sleeves For O...       U.S. NEWS   \n", "1  American Airlines Flyer Charged, Banned For Li...       U.S. NEWS   \n", "2  23 Of The Funniest Tweets About Cats And Dogs ...          COMEDY   \n", "3  The Funniest Tweets From Parents This Week (Se...       PARENTING   \n", "4  Woman Who Called Cops On Black Bird-Watcher Lo...       U.S. NEWS   \n", "5  Cleaner Was Dead In Belk Bathroom For 4 Days B...       U.S. NEWS   \n", "6  Reporter Gets Adorable Surprise From Her Boyfr...       U.S. NEWS   \n", "7  Puerto Ricans Desperate For Water After Hurric...      WORLD NEWS   \n", "8  How A New Documentary Captures The Complexity ...  CULTURE & ARTS   \n", "9  Biden At UN To Call Russian War An Affront To ...      WORLD NEWS   \n", "\n", "                                   short_description               authors  \\\n", "0  Health experts said it is too early to predict...  <PERSON>, AP   \n", "1  He was subdued by passengers and crew when he ...        <PERSON>   \n", "2  \"Until you have a dog you don't understand wha...         <PERSON><PERSON>   \n", "3  \"Accidentally put grown-up toothpaste on my to...      <PERSON>   \n", "4  <PERSON> accused investment firm Franklin Te.<PERSON>.        <PERSON>   \n", "5  The 63-year-old woman was seen working at the ...                   NaN   \n", "6  \"Who's that behind you?\" an anchor for New Yor...         <PERSON><PERSON>   \n", "7  More than half a million people remained witho...       DÁNICA COTO, AP   \n", "8  In \"Mija,\" director <PERSON> combined mus...           <PERSON>   \n", "9  White House officials say the crux of the pres...     <PERSON><PERSON><PERSON>, AP   \n", "\n", "         date  \n", "0  2022-09-23  \n", "1  2022-09-23  \n", "2  2022-09-23  \n", "3  2022-09-23  \n", "4  2022-09-22  \n", "5  2022-09-22  \n", "6  2022-09-22  \n", "7  2022-09-22  \n", "8  2022-09-22  \n", "9  2022-09-21  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "\n", "def show_dataset_head(csv_path, n=5):\n", "    \"\"\"\n", "    Display the first n rows of a CSV file.\n", "    Args:\n", "        csv_path (str): Path to the CSV file.\n", "        n (int): Number of rows to display.\n", "    \"\"\"\n", "    df = pd.read_csv(csv_path)\n", "    display(df.head(n))\n", "\n", "# Usage:\n", "show_dataset_head(\"News_Category_Dataset_v3.csv\", n=10)  # Change n to see more or fewer rows\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "2dc710c2-f271-4b08-899a-8eab2f8b2faf", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Failed to read as a list. Trying line-delimited JSON...\n", "CSV file saved as newsdataset.csv\n"]}], "source": ["import pandas as pd\n", "\n", "input_path = \"News_Category_Dataset_v3.json\"\n", "output_path = \"newsdataset.csv\"\n", "\n", "# Try to read as a list of JSON objects\n", "try:\n", "    df = pd.read_json(input_path)\n", "    df.to_csv(output_path, index=False)\n", "    print(f\"CSV file saved as {output_path}\")\n", "except Exception as e:\n", "    print(\"Failed to read as a list. Trying line-delimited JSON...\")\n", "    try:\n", "        df = pd.read_json(input_path, lines=True)\n", "        df.to_csv(output_path, index=False)\n", "        print(f\"CSV file saved as {output_path}\")\n", "    except Exception as e2:\n", "        print(\"Failed to convert JSON to CSV.\")\n", "        print(\"Error 1:\", e)\n", "        print(\"Error 2:\", e2)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "865e42af-f8c4-4f61-bcf1-e0cba41738aa", "metadata": {}, "outputs": [], "source": ["   import pandas as pd\n", "\n", "   df = pd.read_csv(\"newsdataset.csv\")\n"]}, {"cell_type": "code", "execution_count": 8, "id": "2fcd9c6f-66a4-441d-8162-61f24295f291", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>link</th>\n", "      <th>headline</th>\n", "      <th>category</th>\n", "      <th>short_description</th>\n", "      <th>authors</th>\n", "      <th>date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>https://www.huffpost.com/entry/covid-boosters-...</td>\n", "      <td>Over 4 Million Americans Roll Up Sleeves For O...</td>\n", "      <td>U.S. NEWS</td>\n", "      <td>Health experts said it is too early to predict...</td>\n", "      <td><PERSON>, AP</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>https://www.huffpost.com/entry/american-airlin...</td>\n", "      <td>American Airlines Flyer Charged, Banned For Li...</td>\n", "      <td>U.S. NEWS</td>\n", "      <td>He was subdued by passengers and crew when he ...</td>\n", "      <td><PERSON></td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>https://www.huffpost.com/entry/funniest-tweets...</td>\n", "      <td>23 Of The Funniest Tweets About Cats And Dogs ...</td>\n", "      <td>COMEDY</td>\n", "      <td>\"Until you have a dog you don't understand wha...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>https://www.huffpost.com/entry/funniest-parent...</td>\n", "      <td>The Funniest Tweets From Parents This Week (Se...</td>\n", "      <td>PARENTING</td>\n", "      <td>\"Accidentally put grown-up toothpaste on my to...</td>\n", "      <td><PERSON></td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>https://www.huffpost.com/entry/amy-cooper-lose...</td>\n", "      <td>Woman Who Called Cops On Black Bird-Watcher Lo...</td>\n", "      <td>U.S. NEWS</td>\n", "      <td><PERSON> accused investment firm Franklin Te...</td>\n", "      <td><PERSON></td>\n", "      <td>2022-09-22</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                link  \\\n", "0  https://www.huffpost.com/entry/covid-boosters-...   \n", "1  https://www.huffpost.com/entry/american-airlin...   \n", "2  https://www.huffpost.com/entry/funniest-tweets...   \n", "3  https://www.huffpost.com/entry/funniest-parent...   \n", "4  https://www.huffpost.com/entry/amy-cooper-lose...   \n", "\n", "                                            headline   category  \\\n", "0  Over 4 Million Americans Roll Up Sleeves For O...  U.S. NEWS   \n", "1  American Airlines Flyer Charged, Banned For Li...  U.S. NEWS   \n", "2  23 Of The Funniest Tweets About Cats And Dogs ...     COMEDY   \n", "3  The Funniest Tweets From Parents This Week (Se...  PARENTING   \n", "4  Woman Who Called Cops On Black Bird-Watcher Lo...  U.S. NEWS   \n", "\n", "                                   short_description               authors  \\\n", "0  Health experts said it is too early to predict...  <PERSON>, AP   \n", "1  He was subdued by passengers and crew when he ...        <PERSON>   \n", "2  \"Until you have a dog you don't understand wha...         <PERSON><PERSON>   \n", "3  \"Accidentally put grown-up toothpaste on my to...      <PERSON>   \n", "4  <PERSON> accused investment firm Franklin Te.<PERSON>.        <PERSON>   \n", "\n", "         date  \n", "0  2022-09-23  \n", "1  2022-09-23  \n", "2  2022-09-23  \n", "3  2022-09-23  \n", "4  2022-09-22  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["   df.head()  "]}, {"cell_type": "code", "execution_count": 9, "id": "b16e5712-0964-4a1e-ad99-f6a777108da5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>link</th>\n", "      <th>headline</th>\n", "      <th>category</th>\n", "      <th>short_description</th>\n", "      <th>authors</th>\n", "      <th>date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>https://www.huffpost.com/entry/covid-boosters-...</td>\n", "      <td>Over 4 Million Americans Roll Up Sleeves For O...</td>\n", "      <td>U.S. NEWS</td>\n", "      <td>Health experts said it is too early to predict...</td>\n", "      <td><PERSON>, AP</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>https://www.huffpost.com/entry/american-airlin...</td>\n", "      <td>American Airlines Flyer Charged, Banned For Li...</td>\n", "      <td>U.S. NEWS</td>\n", "      <td>He was subdued by passengers and crew when he ...</td>\n", "      <td><PERSON></td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>https://www.huffpost.com/entry/funniest-tweets...</td>\n", "      <td>23 Of The Funniest Tweets About Cats And Dogs ...</td>\n", "      <td>COMEDY</td>\n", "      <td>\"Until you have a dog you don't understand wha...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>https://www.huffpost.com/entry/funniest-parent...</td>\n", "      <td>The Funniest Tweets From Parents This Week (Se...</td>\n", "      <td>PARENTING</td>\n", "      <td>\"Accidentally put grown-up toothpaste on my to...</td>\n", "      <td><PERSON></td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>https://www.huffpost.com/entry/amy-cooper-lose...</td>\n", "      <td>Woman Who Called Cops On Black Bird-Watcher Lo...</td>\n", "      <td>U.S. NEWS</td>\n", "      <td><PERSON> accused investment firm Franklin Te...</td>\n", "      <td><PERSON></td>\n", "      <td>2022-09-22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>https://www.huffpost.com/entry/belk-worker-fou...</td>\n", "      <td><PERSON><PERSON> Was Dead In Belk Bathroom For 4 Days B...</td>\n", "      <td>U.S. NEWS</td>\n", "      <td>The 63-year-old woman was seen working at the ...</td>\n", "      <td>NaN</td>\n", "      <td>2022-09-22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>https://www.huffpost.com/entry/reporter-gets-a...</td>\n", "      <td>Reporter Gets Adorable Surprise From Her Boyfr...</td>\n", "      <td>U.S. NEWS</td>\n", "      <td>\"Who's that behind you?\" an anchor for New Yor...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>2022-09-22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>https://www.huffpost.com/entry/puerto-rico-wat...</td>\n", "      <td>Puerto Ricans Desperate For Water After Hurric...</td>\n", "      <td>WORLD NEWS</td>\n", "      <td>More than half a million people remained witho...</td>\n", "      <td>DÁNICA COTO, AP</td>\n", "      <td>2022-09-22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>https://www.huffpost.com/entry/mija-documentar...</td>\n", "      <td>How A New Documentary Captures The Complexity ...</td>\n", "      <td>CULTURE &amp; ARTS</td>\n", "      <td>In \"Mija,\" director <PERSON> combined mus...</td>\n", "      <td><PERSON></td>\n", "      <td>2022-09-22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>https://www.huffpost.com/entry/biden-un-russia...</td>\n", "      <td><PERSON><PERSON> At UN To Call Russian War An Affront To ...</td>\n", "      <td>WORLD NEWS</td>\n", "      <td>White House officials say the crux of the pres...</td>\n", "      <td><PERSON><PERSON><PERSON>, AP</td>\n", "      <td>2022-09-21</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                link  \\\n", "0  https://www.huffpost.com/entry/covid-boosters-...   \n", "1  https://www.huffpost.com/entry/american-airlin...   \n", "2  https://www.huffpost.com/entry/funniest-tweets...   \n", "3  https://www.huffpost.com/entry/funniest-parent...   \n", "4  https://www.huffpost.com/entry/amy-cooper-lose...   \n", "5  https://www.huffpost.com/entry/belk-worker-fou...   \n", "6  https://www.huffpost.com/entry/reporter-gets-a...   \n", "7  https://www.huffpost.com/entry/puerto-rico-wat...   \n", "8  https://www.huffpost.com/entry/mija-documentar...   \n", "9  https://www.huffpost.com/entry/biden-un-russia...   \n", "\n", "                                            headline        category  \\\n", "0  Over 4 Million Americans Roll Up Sleeves For O...       U.S. NEWS   \n", "1  American Airlines Flyer Charged, Banned For Li...       U.S. NEWS   \n", "2  23 Of The Funniest Tweets About Cats And Dogs ...          COMEDY   \n", "3  The Funniest Tweets From Parents This Week (Se...       PARENTING   \n", "4  Woman Who Called Cops On Black Bird-Watcher Lo...       U.S. NEWS   \n", "5  Cleaner Was Dead In Belk Bathroom For 4 Days B...       U.S. NEWS   \n", "6  Reporter Gets Adorable Surprise From Her Boyfr...       U.S. NEWS   \n", "7  Puerto Ricans Desperate For Water After Hurric...      WORLD NEWS   \n", "8  How A New Documentary Captures The Complexity ...  CULTURE & ARTS   \n", "9  Biden At UN To Call Russian War An Affront To ...      WORLD NEWS   \n", "\n", "                                   short_description               authors  \\\n", "0  Health experts said it is too early to predict...  <PERSON>, AP   \n", "1  He was subdued by passengers and crew when he ...        <PERSON>   \n", "2  \"Until you have a dog you don't understand wha...         <PERSON><PERSON>   \n", "3  \"Accidentally put grown-up toothpaste on my to...      <PERSON>   \n", "4  <PERSON> accused investment firm Franklin Te.<PERSON>.        <PERSON>   \n", "5  The 63-year-old woman was seen working at the ...                   NaN   \n", "6  \"Who's that behind you?\" an anchor for New Yor...         <PERSON><PERSON>   \n", "7  More than half a million people remained witho...       DÁNICA COTO, AP   \n", "8  In \"Mija,\" director <PERSON> combined mus...           <PERSON>   \n", "9  White House officials say the crux of the pres...     <PERSON><PERSON><PERSON>, AP   \n", "\n", "         date  \n", "0  2022-09-23  \n", "1  2022-09-23  \n", "2  2022-09-23  \n", "3  2022-09-23  \n", "4  2022-09-22  \n", "5  2022-09-22  \n", "6  2022-09-22  \n", "7  2022-09-22  \n", "8  2022-09-22  \n", "9  2022-09-21  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head(10)\n"]}, {"cell_type": "code", "execution_count": 12, "id": "276510fa-85fe-4e96-9ff6-90943da54c7f", "metadata": {}, "outputs": [{"data": {"text/plain": ["0    Over 4 Million Americans Roll Up Sleeves For O...\n", "1    American Airlines Flyer Charged, Banned For Li...\n", "2    23 Of The Funniest Tweets About Cats And Dogs ...\n", "3    The Funniest Tweets From Parents This Week (Se...\n", "Name: headline, dtype: object"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df['headline'].head(4)  \n"]}, {"cell_type": "code", "execution_count": 14, "id": "71009043-772e-4b6a-8ecb-6769ce3da4c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["0    https://www.huffpost.com/entry/covid-boosters-...\n", "1    https://www.huffpost.com/entry/american-airlin...\n", "Name: link, dtype: object"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df['link'].head(2)\n"]}, {"cell_type": "code", "execution_count": 15, "id": "e85fa9c5-3ed5-44b5-92cf-895080b66214", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>headline</th>\n", "      <th>link</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Over 4 Million Americans Roll Up Sleeves For O...</td>\n", "      <td>https://www.huffpost.com/entry/covid-boosters-...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>American Airlines Flyer Charged, Banned For Li...</td>\n", "      <td>https://www.huffpost.com/entry/american-airlin...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>23 Of The Funniest Tweets About Cats And Dogs ...</td>\n", "      <td>https://www.huffpost.com/entry/funniest-tweets...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>The Funniest Tweets From Parents This Week (Se...</td>\n", "      <td>https://www.huffpost.com/entry/funniest-parent...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Woman Who Called Cops On Black Bird-Watcher Lo...</td>\n", "      <td>https://www.huffpost.com/entry/amy-cooper-lose...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td><PERSON><PERSON> Was Dead In Belk Bathroom For 4 Days B...</td>\n", "      <td>https://www.huffpost.com/entry/belk-worker-fou...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            headline  \\\n", "0  Over 4 Million Americans Roll Up Sleeves For O...   \n", "1  American Airlines Flyer Charged, Banned For Li...   \n", "2  23 Of The Funniest Tweets About Cats And Dogs ...   \n", "3  The Funniest Tweets From Parents This Week (Se...   \n", "4  Woman Who Called Cops On Black Bird-Watcher Lo...   \n", "5  Cleaner Was Dead In Belk Bathroom For 4 Days B...   \n", "\n", "                                                link  \n", "0  https://www.huffpost.com/entry/covid-boosters-...  \n", "1  https://www.huffpost.com/entry/american-airlin...  \n", "2  https://www.huffpost.com/entry/funniest-tweets...  \n", "3  https://www.huffpost.com/entry/funniest-parent...  \n", "4  https://www.huffpost.com/entry/amy-cooper-lose...  \n", "5  https://www.huffpost.com/entry/belk-worker-fou...  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df[[ 'headline', 'link']].head(6)"]}, {"cell_type": "code", "execution_count": 17, "id": "dfb61a59-ee84-419b-a876-7bc26b0f3630", "metadata": {}, "outputs": [], "source": ["#link\n", "# headline\n", "# category \n", "# short_discription\n", "# author \n", "# date \n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 19, "id": "805125c9-099d-44c0-890a-51de6fe6cb4a", "metadata": {}, "outputs": [{"data": {"text/plain": ["0     2022-09-23\n", "1     2022-09-23\n", "2     2022-09-23\n", "3     2022-09-23\n", "4     2022-09-22\n", "5     2022-09-22\n", "6     2022-09-22\n", "7     2022-09-22\n", "8     2022-09-22\n", "9     2022-09-21\n", "10    2022-09-21\n", "11    2022-09-21\n", "12    2022-09-21\n", "13    2022-09-21\n", "14    2022-09-21\n", "15    2022-09-20\n", "16    2022-09-20\n", "17    2022-09-20\n", "18    2022-09-20\n", "19    2022-09-20\n", "20    2022-09-20\n", "21    2022-09-19\n", "22    2022-09-19\n", "23    2022-09-19\n", "24    2022-09-19\n", "25    2022-09-19\n", "26    2022-09-19\n", "27    2022-09-18\n", "28    2022-09-18\n", "29    2022-09-18\n", "Name: date, dtype: object"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df['date'].head(30)\n"]}, {"cell_type": "code", "execution_count": 23, "id": "aa4fc0d4-4d4a-49ee-bd79-999089a1fcdc", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "news = pd.read_csv(\"newsdataset.csv\")"]}, {"cell_type": "code", "execution_count": 24, "id": "0b83f70a-96ec-4f20-9e0c-bd49437df0a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["link                     0\n", "headline                 6\n", "category                 0\n", "short_description    19712\n", "authors              37418\n", "date                     0\n", "dtype: int64"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["news.isnull().sum()"]}, {"cell_type": "code", "execution_count": 27, "id": "6df01623-4143-4ece-9332-ab6e19201603", "metadata": {}, "outputs": [], "source": ["news = pd.read_csv(\"newsdataset.csv\")\n"]}, {"cell_type": "code", "execution_count": 33, "id": "393e3ea0-3993-4b25-a873-2e01d42f3055", "metadata": {}, "outputs": [], "source": ["newsdataset =news.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": 35, "id": "5ca83b80-a191-4cc8-9855-8f51adb79678", "metadata": {}, "outputs": [], "source": ["newsdataset = news.duplicated().sum()"]}, {"cell_type": "code", "execution_count": 2, "id": "dc889ebd-6f46-4f2d-882c-80787d86e5e0", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'new_df' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 9\u001b[39m\n\u001b[32m      6\u001b[39m cv = CountVectorizer(max_features=\u001b[32m5000\u001b[39m, stop_words=\u001b[33m'\u001b[39m\u001b[33menglish\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m      8\u001b[39m \u001b[38;5;66;03m# Fit and transform the 'tags' column\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m9\u001b[39m vectors = cv.fit_transform(\u001b[43mnew_df\u001b[49m[\u001b[33m'\u001b[39m\u001b[33mtags\u001b[39m\u001b[33m'\u001b[39m]).toarray()\n\u001b[32m     11\u001b[39m \u001b[38;5;66;03m# You can see the result by printing the shape of the vectors\u001b[39;00m\n\u001b[32m     12\u001b[39m \u001b[38;5;66;03m# This will show (number of news articles, 5000)\u001b[39;00m\n\u001b[32m     13\u001b[39m \u001b[38;5;28mprint\u001b[39m(vectors.shape)\n", "\u001b[31mNameError\u001b[39m: name 'new_df' is not defined"]}], "source": ["from sklearn.feature_extraction.text import CountVectorizer\n", "\n", "# Initialize the CountVectorizer\n", "# max_features=5000 means we'll use the top 5000 most frequent words\n", "# stop_words='english' removes common English words like 'the', 'a', 'in'\n", "cv = CountVectorizer(max_features=5000, stop_words='english')\n", "\n", "# Fit and transform the 'tags' column\n", "vectors = cv.fit_transform(new_df['tags']).toarray()\n", "\n", "# You can see the result by printing the shape of the vectors\n", "# This will show (number of news articles, 5000)\n", "print(vectors.shape)"]}, {"cell_type": "code", "execution_count": 5, "id": "652e88ac-268b-4366-9a7d-2cc7a9beddeb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading and preparing data...\n", "Data prepared.\n", "Vectorizing text...\n", "Text vectorized.\n", "Shape of sparse vectors: (156859, 5000)\n", "Calculating similarity...\n"]}, {"ename": "MemoryError", "evalue": "Unable to allocate 33.1 GiB for an array with shape (4441517579,) and data type int64", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                               <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 26\u001b[39m\n\u001b[32m     23\u001b[39m \u001b[38;5;66;03m# --- Step 3: Calculate Cosine Similarity ---\u001b[39;00m\n\u001b[32m     24\u001b[39m \u001b[38;5;66;03m# This is the next step from the video. It works directly with the sparse matrix.\u001b[39;00m\n\u001b[32m     25\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mCalculating similarity...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m26\u001b[39m similarity = \u001b[43mcosine_similarity\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvectors\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     27\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mSimilarity calculation complete.\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     28\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mShape of similarity matrix:\u001b[39m\u001b[33m\"\u001b[39m, similarity.shape)\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\sklearn\\utils\\_param_validation.py:216\u001b[39m, in \u001b[36mvalidate_params.<locals>.decorator.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    210\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    211\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[32m    212\u001b[39m         skip_parameter_validation=(\n\u001b[32m    213\u001b[39m             prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[32m    214\u001b[39m         )\n\u001b[32m    215\u001b[39m     ):\n\u001b[32m--> \u001b[39m\u001b[32m216\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    217\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m InvalidParameterError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    218\u001b[39m     \u001b[38;5;66;03m# When the function is just a wrapper around an estimator, we allow\u001b[39;00m\n\u001b[32m    219\u001b[39m     \u001b[38;5;66;03m# the function to delegate validation to the estimator, but we replace\u001b[39;00m\n\u001b[32m    220\u001b[39m     \u001b[38;5;66;03m# the name of the estimator by the name of the function in the error\u001b[39;00m\n\u001b[32m    221\u001b[39m     \u001b[38;5;66;03m# message to avoid confusion.\u001b[39;00m\n\u001b[32m    222\u001b[39m     msg = re.sub(\n\u001b[32m    223\u001b[39m         \u001b[33mr\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mparameter of \u001b[39m\u001b[33m\\\u001b[39m\u001b[33mw+ must be\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m    224\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mparameter of \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfunc.\u001b[34m__qualname__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m must be\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m    225\u001b[39m         \u001b[38;5;28mstr\u001b[39m(e),\n\u001b[32m    226\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\sklearn\\metrics\\pairwise.py:1749\u001b[39m, in \u001b[36mcosine_similarity\u001b[39m\u001b[34m(X, Y, dense_output)\u001b[39m\n\u001b[32m   1746\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   1747\u001b[39m     Y_normalized = normalize(Y, copy=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m-> \u001b[39m\u001b[32m1749\u001b[39m K = \u001b[43msafe_sparse_dot\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX_normalized\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mY_normalized\u001b[49m\u001b[43m.\u001b[49m\u001b[43mT\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdense_output\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdense_output\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1751\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m K\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\sklearn\\utils\\extmath.py:203\u001b[39m, in \u001b[36msafe_sparse_dot\u001b[39m\u001b[34m(a, b, dense_output)\u001b[39m\n\u001b[32m    201\u001b[39m         ret = xp.tensordot(a, b, axes=[-\u001b[32m1\u001b[39m, b_axis])\n\u001b[32m    202\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m203\u001b[39m     ret = \u001b[43ma\u001b[49m\u001b[43m \u001b[49m\u001b[43m@\u001b[49m\u001b[43m \u001b[49m\u001b[43mb\u001b[49m\n\u001b[32m    205\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m (\n\u001b[32m    206\u001b[39m     sparse.issparse(a)\n\u001b[32m    207\u001b[39m     \u001b[38;5;129;01mand\u001b[39;00m sparse.issparse(b)\n\u001b[32m    208\u001b[39m     \u001b[38;5;129;01mand\u001b[39;00m dense_output\n\u001b[32m    209\u001b[39m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(ret, \u001b[33m\"\u001b[39m\u001b[33mtoarray\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    210\u001b[39m ):\n\u001b[32m    211\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m ret.toarray()\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\_base.py:732\u001b[39m, in \u001b[36m_spbase.__matmul__\u001b[39m\u001b[34m(self, other)\u001b[39m\n\u001b[32m    729\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m isscalarlike(other):\n\u001b[32m    730\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mV<PERSON>ueError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mScalar operands are not allowed, \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    731\u001b[39m                      \u001b[33m\"\u001b[39m\u001b[33muse \u001b[39m\u001b[33m'\u001b[39m\u001b[33m*\u001b[39m\u001b[33m'\u001b[39m\u001b[33m instead\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m732\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_matmul_dispatch\u001b[49m\u001b[43m(\u001b[49m\u001b[43mother\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\_base.py:636\u001b[39m, in \u001b[36m_spbase._matmul_dispatch\u001b[39m\u001b[34m(self, other)\u001b[39m\n\u001b[32m    632\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m N != other.shape[\u001b[32m0\u001b[39m]:\n\u001b[32m    633\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    634\u001b[39m             \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00merr_prefix\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m (n,k=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mN\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m),(k=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mother.shape[\u001b[32m0\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m,m)->(n,m)\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    635\u001b[39m         )\n\u001b[32m--> \u001b[39m\u001b[32m636\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_matmul_sparse\u001b[49m\u001b[43m(\u001b[49m\u001b[43mother\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    638\u001b[39m \u001b[38;5;66;03m# If it's a list or whatever, treat it like an array\u001b[39;00m\n\u001b[32m    639\u001b[39m other_a = np.asanyarray(other)\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\_compressed.py:585\u001b[39m, in \u001b[36m_cs_matrix._matmul_sparse\u001b[39m\u001b[34m(self, other)\u001b[39m\n\u001b[32m    580\u001b[39m idx_dtype = \u001b[38;5;28mself\u001b[39m._get_index_dtype((\u001b[38;5;28mself\u001b[39m.indptr, \u001b[38;5;28mself\u001b[39m.indices,\n\u001b[32m    581\u001b[39m                              other.indptr, other.indices),\n\u001b[32m    582\u001b[39m                             maxval=nnz)\n\u001b[32m    584\u001b[39m indptr = np.empty(major_dim + \u001b[32m1\u001b[39m, dtype=idx_dtype)\n\u001b[32m--> \u001b[39m\u001b[32m585\u001b[39m indices = \u001b[43mnp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mempty\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnnz\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m=\u001b[49m\u001b[43midx_dtype\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    586\u001b[39m data = np.empty(nnz, dtype=upcast(\u001b[38;5;28mself\u001b[39m.dtype, other.dtype))\n\u001b[32m    588\u001b[39m fn = \u001b[38;5;28mgetattr\u001b[39m(_sparsetools, \u001b[38;5;28mself\u001b[39m.format + \u001b[33m'\u001b[39m\u001b[33m_matmat\u001b[39m\u001b[33m'\u001b[39m)\n", "\u001b[31mMemoryError\u001b[39m: Unable to allocate 33.1 GiB for an array with shape (4441517579,) and data type int64"]}], "source": ["import pandas as pd\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "# --- Step 1: Reload the data and create new_df ---\n", "print(\"Loading and preparing data...\")\n", "news = pd.read_csv(\"newsdataset.csv\")\n", "news.dropna(inplace=True)\n", "news = news[['category', 'headline', 'short_description']]\n", "news['tags'] = news['category'].str.lower() + ' ' + news['headline'].str.lower() + ' ' + news['short_description'].str.lower()\n", "news['id'] = range(len(news))\n", "new_df = news[['id', 'headline', 'tags']]\n", "print(\"Data prepared.\")\n", "\n", "# --- Step 2: Vectorize the text (without .toarray()) ---\n", "print(\"Vectorizing text...\")\n", "cv = CountVectorizer(max_features=5000, stop_words='english')\n", "vectors = cv.fit_transform(new_df['tags']) # <-- .toarray() is removed!\n", "print(\"Text vectorized.\")\n", "print(\"Shape of sparse vectors:\", vectors.shape)\n", "\n", "\n", "# --- Step 3: Calculate Cosine Similarity ---\n", "# This is the next step from the video. It works directly with the sparse matrix.\n", "print(\"Calculating similarity...\")\n", "similarity = cosine_similarity(vectors)\n", "print(\"Similarity calculation complete.\")\n", "print(\"Shape of similarity matrix:\", similarity.shape)"]}, {"cell_type": "code", "execution_count": 7, "id": "e3a0b5fb-9c9c-4f5f-a81f-d7828b2b3c83", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading and preparing data...\n", "Data prepared.\n", "Vectorizing text...\n", "Text vectorized.\n", "Error: Headline 'U.S. Announces 'New Steps' On North Korea Sanctions' not found in the dataset.\n", "\n", "\n", "Error: Headline 'Mom's Viral Post On Why She's 'So Done' With Everything Has A Powerful Message For Parents' not found in the dataset.\n"]}], "source": ["import pandas as pd\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "# --- Step 1: Load and Prepare Data ---\n", "print(\"Loading and preparing data...\")\n", "# It's better to work with a smaller sample to avoid long processing times.\n", "# We'll use the first 20,000 articles for this example.\n", "news = pd.read_csv(\"newsdataset.csv\").head(20000)\n", "news.dropna(inplace=True)\n", "news = news[['category', 'headline', 'short_description']]\n", "# Reset index is important after dropping rows to ensure it's contiguous\n", "news.reset_index(drop=True, inplace=True)\n", "news['tags'] = news['category'].str.lower() + ' ' + news['headline'].str.lower() + ' ' + news['short_description'].str.lower()\n", "news['id'] = news.index # Use the new index as the ID\n", "new_df = news[['id', 'headline', 'tags']].copy()\n", "print(\"Data prepared.\")\n", "\n", "# --- Step 2: Vectorize Text ---\n", "print(\"Vectorizing text...\")\n", "cv = CountVectorizer(max_features=5000, stop_words='english')\n", "vectors = cv.fit_transform(new_df['tags'])\n", "print(\"Text vectorized.\")\n", "\n", "# --- Step 3: The Recommendation Function ---\n", "def recommend(news_headline):\n", "    # Find the index of the article that matches the headline\n", "    try:\n", "        news_index = new_df[new_df['headline'] == news_headline].index[0]\n", "    except IndexError:\n", "        print(f\"Error: Headline '{news_headline}' not found in the dataset.\")\n", "        return\n", "\n", "    # Calculate the cosine similarity for that one article\n", "    # This computes a single row of the similarity matrix, which is memory-efficient\n", "    similarity_scores = cosine_similarity(vectors[news_index], vectors)\n", "    \n", "    # Get a list of (index, similarity_score) tuples and sort them\n", "    similar_articles = sorted(list(enumerate(similarity_scores[0])), reverse=True, key=lambda x: x[1])\n", "    \n", "    # Get the top 5 most similar articles (excluding the article itself)\n", "    print(f\"Recommendations for: '{news_headline}'\\n\")\n", "    for i in similar_articles[1:6]:\n", "        # Get the headline from new_df using the index i[0]\n", "        recommended_headline = new_df.iloc[i[0]].headline\n", "        print(recommended_headline)\n", "\n", "# --- How to Use the Recommender ---\n", "# Pick a headline from your dataset to test it. For example:\n", "# To find a headline to test, you can run: print(new_df['headline'].iloc[150])\n", "\n", "recommend(\"U.S. Announces 'New Steps' On North Korea Sanctions\")\n", "print(\"\\n\")\n", "recommend(\"Mom's Viral Post On Why She's 'So Done' With Everything Has A Powerful Message For Parents\")\n"]}, {"cell_type": "code", "execution_count": 10, "id": "7639f1ff-39be-4cfe-bc17-91db78bf1db1", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"newsdataset.csv\")"]}, {"cell_type": "code", "execution_count": 12, "id": "141ed379-0661-4f6a-ad46-d9fe6bf96c0f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>category</th>\n", "      <th>headline</th>\n", "      <th>short_description</th>\n", "      <th>tags</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>U.S. NEWS</td>\n", "      <td>Over 4 Million Americans Roll Up Sleeves For O...</td>\n", "      <td>Health experts said it is too early to predict...</td>\n", "      <td>u.s. news over 4 million americans roll up sle...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>U.S. NEWS</td>\n", "      <td>American Airlines Flyer Charged, Banned For Li...</td>\n", "      <td>He was subdued by passengers and crew when he ...</td>\n", "      <td>u.s. news american airlines flyer charged, ban...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>COMEDY</td>\n", "      <td>23 Of The Funniest Tweets About Cats And Dogs ...</td>\n", "      <td>\"Until you have a dog you don't understand wha...</td>\n", "      <td>comedy 23 of the funniest tweets about cats an...</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>PARENTING</td>\n", "      <td>The Funniest Tweets From Parents This Week (Se...</td>\n", "      <td>\"Accidentally put grown-up toothpaste on my to...</td>\n", "      <td>parenting the funniest tweets from parents thi...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>U.S. NEWS</td>\n", "      <td>Woman Who Called Cops On Black Bird-Watcher Lo...</td>\n", "      <td><PERSON> accused investment firm Franklin Te...</td>\n", "      <td>u.s. news woman who called cops on black bird-...</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    category                                           headline  \\\n", "0  U.S. NEWS  Over 4 Million Americans Roll Up Sleeves For O...   \n", "1  U.S. NEWS  American Airlines Flyer Charged, Banned For Li...   \n", "2     COMEDY  23 Of The Funniest Tweets About Cats And Dogs ...   \n", "3  PARENTING  The Funniest Tweets From Parents This Week (Se...   \n", "4  U.S. NEWS  Woman Who Called Cops On Black Bird-Watcher Lo...   \n", "\n", "                                   short_description  \\\n", "0  Health experts said it is too early to predict...   \n", "1  He was subdued by passengers and crew when he ...   \n", "2  \"Until you have a dog you don't understand wha...   \n", "3  \"Accidentally put grown-up toothpaste on my to...   \n", "4  <PERSON> accused investment firm Franklin Te...   \n", "\n", "                                                tags  id  \n", "0  u.s. news over 4 million americans roll up sle...   0  \n", "1  u.s. news american airlines flyer charged, ban...   1  \n", "2  comedy 23 of the funniest tweets about cats an...   2  \n", "3  parenting the funniest tweets from parents thi...   3  \n", "4  u.s. news woman who called cops on black bird-...   4  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "db6ed4b3-2fa3-4c75-8b1f-4f00152cded5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading and preparing data...\n", "Data prepared.\n", "Vectorizing text...\n", "Text vectorized.\n", "\n", "--- Recommendations for 'The Best Foods To Eat If You're Hungry At Night' ---\n", "\n", "-> The 20 Funniest Tweets From Women This Week\n", "-> The 20 Funniest Tweets From Women This Week\n", "-> The 20 Funniest Tweets From Women This Week\n", "-> The 20 Funniest Tweets From Women This Week\n", "-> The 20 Funniest Tweets From Women This Week\n"]}], "source": ["\n", "import pandas as pd\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "# --- Step 1: Load and Prepare Data ---\n", "print(\"Loading and preparing data...\")\n", "try:\n", "    news = pd.read_csv(\"newsdataset.csv\")\n", "    news.dropna(inplace=True)\n", "    # Create a new DataFrame to avoid pandas warnings\n", "    new_df = news[['category', 'headline', 'short_description']].copy()\n", "    new_df['tags'] = new_df['category'] + ' ' + new_df['headline'] + ' ' + new_df['short_description']\n", "    print(\"Data prepared.\")\n", "except FileNotFoundError:\n", "    print(\"Error: 'newsdataset.csv' not found. Please make sure the file is in the same directory.\")\n", "    # Stop execution if file is not found\n", "    assert False, \"Dataset not found\"\n", "\n", "\n", "# --- Step 2: Vectorize Text (Memory-Efficient) ---\n", "print(\"Vectorizing text...\")\n", "cv = CountVectorizer(max_features=5000, stop_words='english')\n", "# Create the sparse matrix (no .toarray()!)\n", "vectors = cv.fit_transform(new_df['tags'])\n", "print(\"Text vectorized.\")\n", "\n", "\n", "# --- Step 3: Define the Recommendation Function ---\n", "def recommend(headline):\n", "    \"\"\"\n", "    Recommends 5 similar news articles based on a given headline.\n", "    \"\"\"\n", "    try:\n", "        # Find the integer index of the news article that matches the headline\n", "        news_index = new_df[new_df['headline'] == headline].index[0]\n", "    except IndexError:\n", "        print(f\"\\nError: Headline '{headline}' not found.\")\n", "        print(\"Please copy and paste a headline exactly as it appears in the dataset.\")\n", "        return\n", "\n", "    # Calculate similarity scores for the given article against all others\n", "    similarity_scores = cosine_similarity(vectors[news_index], vectors)\n", "\n", "    # Get the indices of the top 5 most similar articles (excluding itself)\n", "    similar_indices = sorted(list(enumerate(similarity_scores[0])), reverse=True, key=lambda x: x[1])[1:6]\n", "\n", "    # Print the recommended headlines\n", "    print(f\"\\n--- Recommendations for '{headline}' ---\\n\")\n", "    for i in similar_indices:\n", "        print(f\"-> {new_df.iloc[i[0]].headline}\")\n", "\n", "\n", "# --- Step 4: Test the Function with a Real Headline ---\n", "# We will programmatically pick a headline to guarantee it exists.\n", "try:\n", "    # Let's pick an article from the middle of the dataset to test with\n", "    example_headline = new_df.iloc[500].headline\n", "    recommend(example_headline)\n", "except IndexError:\n", "    print(\"\\nCould not get an example headline, the dataset might be too small.\")\n", "\n", "# --- Step 5: ---\n", "# You can now call the function with any headline.\n", "# print(new_df['headline'].head(20))"]}, {"cell_type": "code", "execution_count": 14, "id": "c8c9cad6-2dc0-41f7-a144-ea8d35361b50", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading and preparing data...\n", "Data prepared with 155944 unique articles.\n", "Vectorizing text...\n", "Text vectorized.\n", "\n", "--- Recommendations for '9 Migrants Are Found Dead In Rio Grande: Border Patrol' ---\n", "\n", "-> <PERSON><PERSON><PERSON> Feared Dead In Massive Shopping Mall Blaze In Philippine City Of Davao\n", "-> Record-Breaking Family Migration Overwhelming Resources, Border Officials Say\n", "-> 6 Dead, Including <PERSON><PERSON>, After Molson Coors Facility Shooting In Milwaukee\n", "-> Nicaraguan Man <PERSON> While In Border Patrol Custody In Arizona\n", "-> At Least 57 Dead From Texas Winter Storm, Preliminary Report Says\n"]}], "source": ["# FINAL RECOMMENDER: With Duplicate Removal\n", "\n", "import pandas as pd\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "print(\"Loading and preparing data...\")\n", "try:\n", "    news = pd.read_csv(\"newsdataset.csv\")\n", "    news.dropna(inplace=True)\n", "    \n", "    # --- NEW: Remove duplicate headlines ---\n", "    news.drop_duplicates(subset=['headline'], inplace=True)\n", "    \n", "    # Reset index to ensure it's continuous after dropping rows\n", "    news.reset_index(drop=True, inplace=True)\n", "\n", "    # Create a new DataFrame to avoid pandas warnings\n", "    new_df = news[['category', 'headline', 'short_description']].copy()\n", "    new_df['tags'] = new_df['category'] + ' ' + new_df['headline'] + ' ' + new_df['short_description']\n", "    print(f\"Data prepared with {len(new_df)} unique articles.\")\n", "    \n", "except FileNotFoundError:\n", "    print(\"Error: 'newsdataset.csv' not found.\")\n", "    assert False, \"Dataset not found\"\n", "\n", "# --- Vectorize Text ---\n", "print(\"Vectorizing text...\")\n", "cv = CountVectorizer(max_features=5000, stop_words='english')\n", "vectors = cv.fit_transform(new_df['tags'])\n", "print(\"Text vectorized.\")\n", "\n", "# --- Define the Recommendation Function ---\n", "def recommend(headline):\n", "    try:\n", "        news_index = new_df[new_df['headline'] == headline].index[0]\n", "    except IndexError:\n", "        print(f\"\\nError: Headline '{headline}' not found.\")\n", "        return\n", "\n", "    similarity_scores = cosine_similarity(vectors[news_index], vectors)\n", "    similar_indices = sorted(list(enumerate(similarity_scores[0])), reverse=True, key=lambda x: x[1])[1:6]\n", "\n", "    print(f\"\\n--- Recommendations for '{headline}' ---\\n\")\n", "    for i in similar_indices:\n", "        # Using .iloc to get the headline from the correct position\n", "        print(f\"-> {new_df.iloc[i[0]].headline}\")\n", "\n", "# --- Test the Function ---\n", "try:\n", "    example_headline = new_df.iloc[100].headline # Pick a different article for a new test\n", "    recommend(example_headline)\n", "except IndexError:\n", "    print(\"\\nCould not get an example headline, the dataset might be too small.\")\n"]}, {"cell_type": "code", "execution_count": 15, "id": "433577db-2b67-4a09-b16c-b833c545539c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["U.S. NEWS Over 4 Million Americans Roll Up Sleeves For Omicron-Targeted COVID Boosters Health experts said it is too early to predict whether demand would match up with the 171 million doses of the new boosters the U.S. ordered for the fall.\n"]}], "source": ["print(new_df.iloc[0].tags)\n"]}, {"cell_type": "code", "execution_count": 16, "id": "ccd739ee-ef23-465b-bcf5-27e49bfc4ddf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PARENTING The Funniest Tweets From Parents This Week (Sept. 17-23) \"Accidentally put grown-up toothpaste on my toddler’s toothbrush and he screamed like I was cleaning his teeth with a Carolina Reaper dipped in Tabasco sauce.\"\n"]}], "source": ["print(new_df.iloc[3].tags)"]}, {"cell_type": "code", "execution_count": 17, "id": "4e0ebe07-af00-448e-8730-085cb27da38a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WORLD NEWS UN To Scale Up Humanitarian Operations In Ukraine Following Russia's Invasion The UN will allocate $20 million to “help with health care, shelter, food, and water and sanitation to the most vulnerable people affected by the conflict.”\n"]}], "source": ["print(new_df.iloc[1000].tags)"]}, {"cell_type": "code", "execution_count": 18, "id": "c68bd7a0-03f3-497d-87a4-856cc2aa2c96", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0     Over 4 Million Americans Roll Up Sleeves For O...\n", "1     American Airlines Flyer Charged, Banned For Li...\n", "2     23 Of The Funniest Tweets About Cats And Dogs ...\n", "3     The Funniest Tweets From Parents This Week (Se...\n", "4     Woman Who Called Cops On Black Bird-Watcher Lo...\n", "5     Reporter Gets Adorable Surprise From Her Boyfr...\n", "6     Puerto Ricans Desperate For Water After Hurric...\n", "7     How A New Documentary Captures The Complexity ...\n", "8     Biden At UN To Call Russian War An Affront To ...\n", "9     World Cup Captains Want To Wear Rainbow Armban...\n", "10    Man Sets Himself On Fire In Apparent Protest O...\n", "11    <PERSON> To Become Category 4 Storm Hea...\n", "12    Twitch Bans Gambling Sites After Streamer Scam...\n", "13    <PERSON> Thomas Agrees To Interview With Jan. ...\n", "14    Russian Cosmonaut <PERSON><PERSON> Who Broke Re...\n", "15    'Reboot' Is A Clever And Not Too Navel-Gazey L...\n", "16    <PERSON><PERSON>, <PERSON>-<PERSON><PERSON>ing Shortstop For Dodge...\n", "17    4 Russian-Controlled Ukrainian Regions Schedul...\n", "18    <PERSON> Toward Turks And Caicos Islands ...\n", "19    Hurricane <PERSON> Down On Dominican Republ...\n", "Name: headline, dtype: object\n"]}], "source": ["print(new_df['headline'].head(20))"]}, {"cell_type": "code", "execution_count": 1, "id": "894940f7-f6b0-4e29-85c2-c66ebd0b1ed5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "import random"]}, {"cell_type": "code", "execution_count": 2, "id": "4e66da70-5db2-4088-a9c3-475d89c7846b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading and preparing data...\n", "Data prepared successfully. We have 155944 unique articles.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>headline</th>\n", "      <th>tags</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Over 4 Million Americans Roll Up Sleeves For O...</td>\n", "      <td>U.S. NEWS Over 4 Million Americans Roll Up Sle...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>American Airlines Flyer Charged, Banned For Li...</td>\n", "      <td>U.S. NEWS American Airlines Flyer Charged, Ban...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>23 Of The Funniest Tweets About Cats And Dogs ...</td>\n", "      <td>COMEDY 23 Of The Funniest Tweets About Cats An...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>The Funniest Tweets From Parents This Week (Se...</td>\n", "      <td>PARENTING The Funniest Tweets From Parents Thi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Woman Who Called Cops On Black Bird-Watcher Lo...</td>\n", "      <td>U.S. NEWS Woman Who Called Cops On Black Bird-...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            headline  \\\n", "0  Over 4 Million Americans Roll Up Sleeves For O...   \n", "1  American Airlines Flyer Charged, Banned For Li...   \n", "2  23 Of The Funniest Tweets About Cats And Dogs ...   \n", "3  The Funniest Tweets From Parents This Week (Se...   \n", "4  Woman Who Called Cops On Black Bird-Watcher Lo...   \n", "\n", "                                                tags  \n", "0  U.S. NEWS Over 4 Million Americans Roll Up Sle...  \n", "1  U.S. NEWS American Airlines Flyer Charged, Ban...  \n", "2  COMEDY 23 Of The Funniest Tweets About Cats An...  \n", "3  PARENTING The Funniest Tweets From Parents Thi...  \n", "4  U.S. NEWS Woman Who Called Cops On Black Bird-...  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"Loading and preparing data...\")\n", "# Load the dataset from CSV\n", "news = pd.read_csv(\"newsdataset.csv\")\n", "\n", "# Drop rows with any missing values\n", "news.dropna(inplace=True)\n", "\n", "# --- ENHANCEMENT: Remove Duplicate Headlines ---\n", "# Keep only the first occurrence of each headline to improve recommendation quality\n", "news.drop_duplicates(subset='headline', keep='first', inplace=True)\n", "\n", "# Create the 'tags' feature by combining text columns\n", "news['tags'] = news['category'] + ' ' + news['headline'] + ' ' + news['short_description']\n", "\n", "# Create a final, clean DataFrame. Resetting the index is crucial.\n", "new_df = news[['headline', 'tags']].reset_index(drop=True)\n", "\n", "print(f\"Data prepared successfully. We have {len(new_df)} unique articles.\")\n", "new_df.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "4f7b5317-2866-4fc3-b231-2be316639c4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Vectorizing text with TF-IDF...\n", "Text vectorized successfully.\n", "Shape of article vectors matrix: (155944, 5000)\n"]}], "source": ["print(\"Vectorizing text with TF-IDF...\")\n", "\n", "# Initialize the TF-IDF Vectorizer\n", "# We'll use the top 5000 words and ignore common English stop words.\n", "tfidf = TfidfVectorizer(max_features=5000, stop_words='english')\n", "\n", "# Fit the vectorizer to our data and transform the 'tags' column into a matrix\n", "article_vectors = tfidf.fit_transform(new_df['tags'])\n", "\n", "print(\"Text vectorized successfully.\")\n", "print(\"Shape of article vectors matrix:\", article_vectors.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "04a9b1f6-8dd6-4d36-a790-504a1ad3c271", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.4"}}, "nbformat": 4, "nbformat_minor": 5}